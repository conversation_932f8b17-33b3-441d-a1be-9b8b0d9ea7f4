import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv, PluginOption } from 'vite';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import viteCompression from 'vite-plugin-compression';
import { buildConfig } from './src/utils/build';
import generateWebConfig from './src/utils/generateWebConfig.js';
import { updateVersionJson } from './src/utils/versionUtils';
import path from 'path';

// 自定义插件-自动生成版本号
const generateVersionJson: PluginOption = {
	name: 'generate-version-json',
	apply: 'build',
	closeBundle() {
		const publicPath = path.resolve(__dirname, 'public');
		const distPath = path.resolve(__dirname, 'dist');
		updateVersionJson(publicPath, distPath);
	},
};

const pathResolve = (dir: string) => {
	return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
	'/@': pathResolve('./src/'),
	'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};

const viteConfig = defineConfig((mode: ConfigEnv) => {
	const env = loadEnv(mode.mode, process.cwd());
	return {
		plugins: [
			vue(),
			vueSetupExtend(),
			viteCompression(),
			JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null,
			generateWebConfig({
				version: `<?xml version="1.0" encoding="UTF-8"?>
				<configuration>
					<system.webServer>
						<rewrite>
							<rules>
								<rule name="HTTP TO HTTPS" stopProcessing="true">
									<match url="(.*)" />
									<conditions>
										<add input="{HTTPS}" pattern="^OFF$" />
										<add input="{REMOTE_ADDR}" pattern="^192\.168\." negate="true" />
									</conditions>
									<action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="SeeOther" />
								</rule>
							</rules>
						</rewrite>
					</system.webServer>

					<location path="index.html">
						<system.webServer>
							<httpProtocol>
								<customHeaders>
								<add name="Cache-Control" value="no-store, max-age=0" />
								</customHeaders>
							</httpProtocol>
						</system.webServer>
					</location>
				</configuration>
								`,
			}),
			generateVersionJson,
		],
		root: process.cwd(),
		resolve: { alias },
		base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
		optimizeDeps: { exclude: ['vue-demi'] },
		server: {
			host: true,
			port: env.VITE_PORT as unknown as number,
			open: JSON.parse(env.VITE_OPEN),
			hmr: true,
			https: {
				maxSessionMemory: 100,
			},
			proxy: {
				'/gitee': {
					target: 'https://gitee.com',
					ws: true,
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/gitee/, ''),
				},
				'/api': {
					target: 'http://127.0.0.1:7000',
					ws: true,
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/api/, ''),
				},
			},
		},
		build: {
			outDir: 'dist',
			chunkSizeWarningLimit: 1500,
			rollupOptions: {
				output: {
					chunkFileNames: 'assets/js/[name]-[hash].js',
					entryFileNames: 'assets/js/[name]-[hash].js',
					assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!.moduleName ?? 'vender';
						}
					},
				},
				...(JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}),
			},
		},
		css: { preprocessorOptions: { css: { charset: false } } },
		define: {
			__VUE_I18N_LEGACY_API__: JSON.stringify(false),
			__VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
			__INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
			__NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
		},
	};
});

export default viteConfig;
