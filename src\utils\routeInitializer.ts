import { Local } from '/@/utils/storage';
import { useThemeConfig } from '/@/stores/themeConfig';
import { storeToRefs } from 'pinia';
import pinia from '/@/stores/index';

/**
 * 应用启动时的路由预初始化
 * 确保在应用挂载前完成必要的路由初始化
 */
export async function initRouteOnStartup(): Promise<void> {
	try {
		// 检查是否有token，没有token则跳过初始化
		const token = Local.get('token');
		if (!token) {
			console.log('No token found, skipping route initialization');
			return;
		}

		// 检查是否启用后端控制路由
		const storesThemeConfig = useThemeConfig(pinia);
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { isRequestRoutes } = themeConfig.value;

		if (isRequestRoutes) {
			// 动态导入后端路由初始化函数，避免循环依赖
			const { initBackEndControlRoutes } = await import('/@/router/backEnd');
			console.log('Pre-initializing backend routes...');
			await initBackEndControlRoutes();
			console.log('Backend routes pre-initialized successfully');
		} else {
			// 动态导入前端路由初始化函数
			const { initFrontEndControlRoutes } = await import('/@/router/frontEnd');
			console.log('Pre-initializing frontend routes...');
			await initFrontEndControlRoutes();
			console.log('Frontend routes pre-initialized successfully');
		}
	} catch (error) {
		console.warn('Route pre-initialization failed:', error);
		// 不抛出错误，让应用继续启动
	}
}

/**
 * 路由守卫的备用初始化
 * 当预初始化失败或路由列表为空时使用
 */
export async function ensureRoutesInitialized(): Promise<boolean> {
	try {
		const token = Local.get('token');
		if (!token) return false;

		const storesThemeConfig = useThemeConfig(pinia);
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { isRequestRoutes } = themeConfig.value;

		if (isRequestRoutes) {
			const { initBackEndControlRoutes } = await import('/@/router/backEnd');
			return await initBackEndControlRoutes();
		} else {
			const { initFrontEndControlRoutes } = await import('/@/router/frontEnd');
			return await initFrontEndControlRoutes();
		}
	} catch (error) {
		console.error('Route initialization failed:', error);
		return false;
	}
}
